# Dashboard System - Trustay Web

## Tổng quan

Hệ thống dashboard đã được tạo với 2 loại người dùng có thể chuyển đổi vai trò:
- **Người thuê trọ (Tenant)**: <PERSON><PERSON><PERSON><PERSON> lý thông tin lưu trú, tì<PERSON> kiếm phòng trọ
- **<PERSON><PERSON> trọ (Landlord)**: <PERSON><PERSON><PERSON><PERSON> lý phòng trọ, kh<PERSON><PERSON> thuê, quảng cáo

## ✨ Tính năng mới: Chuyển đổi vai trò
- Người dùng có thể chuyển đổi giữa vai trò "Ng<PERSON>ời thuê" và "Chủ trọ"
- <PERSON><PERSON><PERSON> "Quản cáo trọ" xuất hiện bên cạnh user menu cho người thuê
- Nút "Chế độ thuê trọ" xuất hiện cho chủ trọ
- Tự động chuyển hướng đến dashboard phù hợp sau khi chuyển vai trò

## Tài khoản Demo

### Ng<PERSON><PERSON><PERSON> thuê trọ
- **Email**: `<EMAIL>`
- **M<PERSON><PERSON> khẩu**: bất kỳ
- **Dashboard**: `/dashboard/tenant`

### Chủ trọ  
- **Email**: `<EMAIL>`
- **Mật khẩu**: bất kỳ
- **Dashboard**: `/dashboard/landlord`

## Cấu trúc Files

### Components Dashboard
```
src/components/dashboard/
├── dashboard-layout.tsx    # Layout chung cho dashboard
├── sidebar.tsx            # Sidebar navigation
└── stat-card.tsx         # Component hiển thị thống kê
```

### Pages Dashboard
```
src/app/dashboard/
├── tenant/
│   ├── page.tsx                    # Dashboard chính người thuê
│   ├── profile/page.tsx           # Thông tin cá nhân
│   └── accommodation/page.tsx     # Thông tin lưu trú
└── landlord/
    ├── page.tsx                   # Dashboard chính chủ trọ
    ├── properties/page.tsx        # Quản lý trọ
    └── advertising/page.tsx       # Quảng cáo trọ
```

### Data & Store
```
src/data/demo-users.ts     # Dữ liệu tài khoản demo
src/data/mock-data.ts      # Dữ liệu giả cho properties, rooms, bookings
src/stores/user-store.ts   # Store quản lý user (đã cập nhật userType + switchRole)
```

## Tính năng chính

### Dashboard Người thuê trọ
- **Quản lý lưu trú**: Xem thông tin nơi ở hiện tại
- **Thông tin cá nhân**: Cập nhật hồ sơ, CCCD
- **Tìm kiếm phòng**: Tìm và lưu phòng yêu thích
- **Đánh giá**: Quản lý đánh giá đã viết
- **Thông báo**: Nhận thông báo từ chủ trọ

### Dashboard Chủ trọ
- **Tổng quan**: Thống kê tổng quan kinh doanh
- **Quản lý trọ**: Thêm, sửa, xóa nhà trọ
- **Quảng cáo**: Tạo và quản lý quảng cáo
- **Quản lý cho thuê**: Theo dõi hợp đồng, khách thuê
- **Hỗ trợ**: Liên hệ nhân viên hỗ trợ

## Cách sử dụng

1. **Đăng nhập**: Truy cập `/login` và sử dụng tài khoản demo
2. **Auto redirect**: Hệ thống tự động chuyển đến dashboard phù hợp
3. **Navigation**: Sử dụng sidebar để điều hướng giữa các trang
4. **User menu**: Click vào tên user ở header để truy cập nhanh dashboard

## Routing Logic

- Sau khi đăng nhập/đăng ký, user được chuyển đến dashboard phù hợp
- `DashboardLayout` kiểm tra userType và redirect nếu không khớp
- Navigation menu hiển thị link dashboard dựa trên userType
- Logout sẽ xóa session và chuyển về trang chủ

## Responsive Design

- Dashboard responsive trên mobile, tablet, desktop
- Sidebar có thể collapse trên mobile
- Cards và stats tự động điều chỉnh layout

## Mở rộng

Để thêm tính năng mới:
1. Tạo page mới trong thư mục dashboard tương ứng
2. Thêm route vào sidebar items
3. Implement logic nghiệp vụ
4. Cập nhật navigation nếu cần

## 📊 Dữ liệu Demo

Hệ thống hiện có dữ liệu giả phong phú:
- **3 nhà trọ** với thông tin chi tiết (Sunshine, Green House, Blue Sky)
- **Phòng trọ** với giá cả, diện tích, tiện nghi
- **Hợp đồng thuê** đang hoạt động
- **Đánh giá** từ người thuê
- **Phòng đã lưu** cho người thuê

## 🔄 Chuyển đổi vai trò

1. **Từ Tenant → Landlord**: Click "Quản cáo trọ" → Chuyển sang dashboard quản lý
2. **Từ Landlord → Tenant**: Click "Chế độ thuê trọ" → Chuyển sang dashboard người thuê
3. **Tự động redirect**: Hệ thống tự động chuyển đến dashboard phù hợp
4. **Persistent**: Vai trò được lưu trong localStorage

## Lưu ý

- Tất cả dữ liệu hiện tại là demo/mock data với thông tin phong phú
- Dashboard hiển thị dữ liệu thực tế thay vì empty state
- Cần tích hợp API thực tế cho production
- Authentication hiện tại chỉ là demo, cần implement JWT/OAuth
- Cần thêm middleware bảo mật cho routes dashboard
