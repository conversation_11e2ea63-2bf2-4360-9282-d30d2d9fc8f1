// Mock data for properties, rooms, and bookings

export interface Property {
  id: string;
  name: string;
  address: string;
  district: string;
  city: string;
  totalRooms: number;
  occupiedRooms: number;
  monthlyRevenue: number;
  images: string[];
  description: string;
  amenities: string[];
  createdAt: string;
  status: 'active' | 'inactive';
}

export interface Room {
  id: string;
  propertyId: string;
  roomNumber: string;
  price: number;
  area: number;
  status: 'available' | 'occupied' | 'maintenance';
  tenantId?: string;
  tenantName?: string;
  tenantPhone?: string;
  contractStart?: string;
  contractEnd?: string;
  images: string[];
  amenities: string[];
}

export interface Booking {
  id: string;
  propertyId: string;
  roomId: string;
  tenantId: string;
  tenantName: string;
  tenantPhone: string;
  tenantEmail: string;
  checkIn: string;
  checkOut?: string;
  monthlyRent: number;
  deposit: number;
  status: 'active' | 'pending' | 'expired' | 'cancelled';
  contractSigned: boolean;
}

export interface SavedProperty {
  id: string;
  propertyId: string;
  tenantId: string;
  savedAt: string;
}

export interface Review {
  id: string;
  propertyId: string;
  tenantId: string;
  tenantName: string;
  rating: number;
  comment: string;
  createdAt: string;
  images?: string[];
}

// Mock Properties
export const mockProperties: Property[] = [
  {
    id: 'prop-1',
    name: 'Nhà trọ Sunshine',
    address: '123 Đường Nguyễn Văn Cừ',
    district: 'Quận 1',
    city: 'TP.HCM',
    totalRooms: 12,
    occupiedRooms: 10,
    monthlyRevenue: 25000000,
    images: ['/images/property1.jpg', '/images/property1-2.jpg'],
    description: 'Nhà trọ cao cấp, đầy đủ tiện nghi, gần trường đại học',
    amenities: ['WiFi miễn phí', 'Điều hòa', 'Thang máy', 'Bảo vệ 24/7'],
    createdAt: '2024-01-15',
    status: 'active'
  },
  {
    id: 'prop-2',
    name: 'Phòng trọ Green House',
    address: '456 Đường Lê Văn Sỹ',
    district: 'Quận 3',
    city: 'TP.HCM',
    totalRooms: 8,
    occupiedRooms: 6,
    monthlyRevenue: 18000000,
    images: ['/images/property2.jpg'],
    description: 'Phòng trọ yên tĩnh, sạch sẽ, gần chợ và siêu thị',
    amenities: ['WiFi', 'Máy giặt chung', 'Bếp chung', 'Gửi xe miễn phí'],
    createdAt: '2024-02-01',
    status: 'active'
  },
  {
    id: 'prop-3',
    name: 'Ký túc xá Blue Sky',
    address: '789 Đường Võ Văn Tần',
    district: 'Quận 10',
    city: 'TP.HCM',
    totalRooms: 20,
    occupiedRooms: 15,
    monthlyRevenue: 35000000,
    images: ['/images/property3.jpg'],
    description: 'Ký túc xá hiện đại dành cho sinh viên và người đi làm',
    amenities: ['WiFi tốc độ cao', 'Gym', 'Sân thượng', 'Căng tin'],
    createdAt: '2024-01-10',
    status: 'active'
  }
];

// Mock Rooms
export const mockRooms: Room[] = [
  {
    id: 'room-1',
    propertyId: 'prop-1',
    roomNumber: 'A101',
    price: 2500000,
    area: 25,
    status: 'occupied',
    tenantId: 'tenant-1',
    tenantName: 'Nguyễn Văn An',
    tenantPhone: '0123456789',
    contractStart: '2024-01-01',
    contractEnd: '2024-12-31',
    images: ['/images/room1.jpg'],
    amenities: ['Điều hòa', 'Tủ lạnh', 'Giường', 'Tủ quần áo']
  },
  {
    id: 'room-2',
    propertyId: 'prop-1',
    roomNumber: 'A102',
    price: 2800000,
    area: 30,
    status: 'available',
    images: ['/images/room2.jpg'],
    amenities: ['Điều hòa', 'Tủ lạnh', 'Giường', 'Tủ quần áo', 'Ban công']
  },
  {
    id: 'room-3',
    propertyId: 'prop-2',
    roomNumber: 'B201',
    price: 2200000,
    area: 20,
    status: 'occupied',
    tenantId: 'tenant-2',
    tenantName: 'Trần Thị Bình',
    tenantPhone: '0987654321',
    contractStart: '2024-02-01',
    contractEnd: '2024-12-31',
    images: ['/images/room3.jpg'],
    amenities: ['Điều hòa', 'Giường', 'Tủ quần áo']
  }
];

// Mock Bookings
export const mockBookings: Booking[] = [
  {
    id: 'booking-1',
    propertyId: 'prop-1',
    roomId: 'room-1',
    tenantId: 'tenant-1',
    tenantName: 'Nguyễn Văn An',
    tenantPhone: '0123456789',
    tenantEmail: '<EMAIL>',
    checkIn: '2024-01-01',
    monthlyRent: 2500000,
    deposit: 5000000,
    status: 'active',
    contractSigned: true
  },
  {
    id: 'booking-2',
    propertyId: 'prop-2',
    roomId: 'room-3',
    tenantId: 'tenant-2',
    tenantName: 'Trần Thị Bình',
    tenantPhone: '0987654321',
    tenantEmail: '<EMAIL>',
    checkIn: '2024-02-01',
    monthlyRent: 2200000,
    deposit: 4400000,
    status: 'active',
    contractSigned: true
  }
];

// Mock Saved Properties
export const mockSavedProperties: SavedProperty[] = [
  {
    id: 'saved-1',
    propertyId: 'prop-2',
    tenantId: 'tenant-1',
    savedAt: '2024-01-20'
  },
  {
    id: 'saved-2',
    propertyId: 'prop-3',
    tenantId: 'tenant-1',
    savedAt: '2024-01-25'
  }
];

// Mock Reviews
export const mockReviews: Review[] = [
  {
    id: 'review-1',
    propertyId: 'prop-1',
    tenantId: 'tenant-1',
    tenantName: 'Nguyễn Văn An',
    rating: 5,
    comment: 'Phòng trọ rất tốt, sạch sẽ, chủ trọ thân thiện. Rất hài lòng!',
    createdAt: '2024-01-15',
    images: ['/images/review1.jpg']
  },
  {
    id: 'review-2',
    propertyId: 'prop-2',
    tenantId: 'tenant-2',
    tenantName: 'Trần Thị Bình',
    rating: 4,
    comment: 'Vị trí thuận tiện, giá cả hợp lý. Chỉ có điều hơi ồn vào buổi tối.',
    createdAt: '2024-02-10'
  }
];

// Helper functions
export const getPropertiesByOwner = (ownerId: string): Property[] => {
  // In real app, filter by ownerId
  return mockProperties;
};

export const getRoomsByProperty = (propertyId: string): Room[] => {
  return mockRooms.filter(room => room.propertyId === propertyId);
};

export const getBookingsByProperty = (propertyId: string): Booking[] => {
  return mockBookings.filter(booking => booking.propertyId === propertyId);
};

export const getSavedPropertiesByTenant = (tenantId: string): Property[] => {
  const savedPropertyIds = mockSavedProperties
    .filter(saved => saved.tenantId === tenantId)
    .map(saved => saved.propertyId);
  
  return mockProperties.filter(property => savedPropertyIds.includes(property.id));
};

export const getReviewsByTenant = (tenantId: string): Review[] => {
  return mockReviews.filter(review => review.tenantId === tenantId);
};
